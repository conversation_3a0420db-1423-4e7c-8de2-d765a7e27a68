"""Google Cloud Pub/Sub notification service for Marine HMM Reports API."""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional
from google.cloud import pubsub_v1
from google.cloud.exceptions import NotFound
from flask import current_app
from config.settings import get_users_for_tenant

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for handling Google Cloud Pub/Sub notifications."""
    
    def __init__(self, config: Optional[Dict] = None):
        """Initialize notification service.
        
        Args:
            config: Optional config dict, uses Flask config if not provided
        """
        self.config = config or self._get_flask_config()
        self.publisher = None
        self.topic_path = None
        # Initialize publisher during construction for reliable operation
        self._initialize_publisher()
    
    def _get_flask_config(self) -> Dict:
        """Get Pub/Sub configuration from Flask app config.
        
        Returns:
            Pub/Sub configuration dictionary
        """
        import os
        
        # Always use environment variables directly to ensure consistency
        config = {
            'project_id': os.getenv('PROJECT_ID', 'prj-nonprod-eng-svc-01'),
            'bucket_name': os.getenv('HMM_REPORTS_BUCKET_NAME', 'hull-nonprod-static-content'),
            'pubsub_topic': os.getenv('PUBSUB_TOPIC', 'marine-reports'),
            'tenant_users_config': os.getenv('TENANT_USERS_CONFIG', '')
        }
        
        # Try to get from Flask config if available, but use env vars as fallback
        try:
            gcp_config = current_app.config.get('GCP_CONFIG', {})
            tenant_config = current_app.config.get('TENANT_CONFIG', {})
            
            if gcp_config.get('project_id'):
                config['project_id'] = gcp_config['project_id']
            if gcp_config.get('pubsub_topic'):
                config['pubsub_topic'] = gcp_config['pubsub_topic']
            if tenant_config.get('tenant_users_config'):
                config['tenant_users_config'] = tenant_config['tenant_users_config']
                
        except (RuntimeError, KeyError):
            # Flask context not available, use environment variables
            pass
        
        return config
    
    def _initialize_publisher(self):
        """Initialize Pub/Sub publisher client."""
        try:
            if not self.config['pubsub_topic']:
                logger.warning("Pub/Sub topic not configured, notifications will be disabled")
                return
            
            self.publisher = pubsub_v1.PublisherClient()
            self.topic_path = self.publisher.topic_path(
                self.config['project_id'], 
                self.config['pubsub_topic']
            )
            
            logger.info(f"Pub/Sub publisher initialized for topic: {self.topic_path}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Pub/Sub publisher: {e}")
            raise
    
    def _ensure_publisher(self):
        """Ensure Pub/Sub publisher is initialized."""
        if self.publisher is None or self.topic_path is None:
            self._initialize_publisher()
    
    def publish_report_notification(self, tenant_id: str, report_path: str, 
                                  filename: str) -> List[str]:
        """Publish report notification messages for tenant users.
        
        Args:
            tenant_id: Tenant identifier
            report_path: GCS path of the generated report
            filename: Report filename
            
        Returns:
            List of message IDs that were published
        """
        try:
            self._ensure_publisher()
        except Exception as e:
            logger.warning(f"Failed to initialize Pub/Sub publisher: {e}")
            return []
        
        if not self.publisher or not self.topic_path:
            logger.warning("Pub/Sub not configured, skipping notification")
            return []
        
        try:
            # Get users for the tenant
            users = get_users_for_tenant(tenant_id, self.config['tenant_users_config'])
            
            if not users:
                logger.warning(f"No users configured for tenant: {tenant_id}")
                return []
            
            logger.debug(f"Found {len(users)} users for tenant {tenant_id}: {[u.get('email') for u in users]}")
            
            message_ids = []
            
            for user in users:
                message_id = self._publish_user_notification(
                    tenant_id, user, report_path, filename
                )
                if message_id:
                    message_ids.append(message_id)
            
            logger.info(f"Published {len(message_ids)} notification messages for tenant: {tenant_id}")
            return message_ids
            
        except Exception as e:
            logger.error(f"Error publishing report notifications: {e}")
            return []
    
    def _publish_user_notification(self, tenant_id: str, user: Dict, 
                                 report_path: str, filename: str) -> Optional[str]:
        """Publish notification message for a specific user.
        
        Args:
            tenant_id: Tenant identifier
            user: User information dictionary
            report_path: GCS path of the report
            filename: Report filename
            
        Returns:
            Message ID if successful, None otherwise
        """
        try:
            # Format report name as "Portfolio HMM Report DD-MM-YYYY"
            report_name = self._format_report_name(filename)
            
            # Generate pre-signed URL for the report
            report_location = self._get_report_location(report_path, filename)
            
            # Create QuestMessageEnvelope format
            message_data = {
                'messageId': str(uuid.uuid4()),
                'messageSender': 'hmm-reports-service',
                'messageType': 'PORTFOLIO_HMM_REPORT',
                'messageBody': {
                    'customerName': user.get('name', ''),
                    'reportLocation': report_location,
                    'reportName': report_name,
                    'userEmail': user.get('email', '')
                },
                'sentAt': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%S+0000'),
                'tenantID': tenant_id
            }
            
            # Convert to JSON string
            message_json = json.dumps(message_data)
            message_bytes = message_json.encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(self.topic_path, message_bytes)
            message_id = future.result()
            
            logger.info(f"Published notification for user {user.get('email')}: {message_id}")
            return message_id
            
        except Exception as e:
            logger.error(f"Error publishing user notification: {e}")
            return None
    
    def _get_report_location(self, gcs_path: str, filename: str) -> str:
        """Get pre-signed URL for report or return # if failed.
        
        Args:
            gcs_path: GCS path of the report
            filename: Report filename
            
        Returns:
            Pre-signed URL or "#" if generation failed
        """
        try:
            # Import storage service to generate signed URL
            from services.storage_service import StorageService
            storage_service = StorageService(self.config)
            
            # Extract blob name from GCS path
            # gs://bucket-name/filename -> filename
            blob_name = gcs_path.split('/')[-1]
            
            # Generate signed URL (7 days expiry)
            signed_url = storage_service.generate_signed_url(blob_name, expiration_hours=168)
            
            if signed_url:
                logger.debug(f"Generated signed URL for {filename}")
                return signed_url
            else:
                logger.warning(f"Failed to generate signed URL for {filename}, using fallback")
                return "#"
                
        except Exception as e:
            logger.warning(f"Error generating signed URL for {filename}: {e}, using fallback")
            return "#"
    
    def _format_report_name(self, filename: str) -> str:
        """Format report name from filename to readable format.
        
        Args:
            filename: Original filename like "Portfolio_HMM_Report_tenant_20250813_224324.xlsx"
            
        Returns:
            Formatted name like "Portfolio HMM Report 13-08-2025"
        """
        try:
            # Extract date from filename (format: YYYYMMDD_HHMMSS)
            # Example: Portfolio_HMM_Report_cea7760d-71ec-4f57-b65d-24655de59a43_20250813_224324.xlsx
            parts = filename.replace('.xlsx', '').split('_')
            
            # Find the date part (8 digits)
            date_part = None
            for part in parts:
                if len(part) == 8 and part.isdigit():
                    date_part = part
                    break
            
            if date_part:
                # Convert YYYYMMDD to DD-MM-YYYY
                year = date_part[:4]
                month = date_part[4:6]
                day = date_part[6:8]
                formatted_date = f"{day}-{month}-{year}"
                return f"Portfolio HMM Report {formatted_date}"
            else:
                # Fallback if date not found
                return "Portfolio HMM Report"
                
        except Exception as e:
            logger.warning(f"Could not format report name from {filename}: {e}")
            return "Portfolio HMM Report"
    
    def publish_job_status_notification(self, job_id: str, status: str, 
                                      tenant_id: str = None, 
                                      additional_data: Dict = None) -> Optional[str]:
        """Publish job status notification.
        
        Args:
            job_id: Job identifier
            status: Job status (pending, running, completed, failed)
            tenant_id: Optional tenant identifier
            additional_data: Optional additional data to include
            
        Returns:
            Message ID if successful, None otherwise
        """
        if not self.publisher or not self.topic_path:
            logger.warning("Pub/Sub not configured, skipping notification")
            return None
        
        try:
            message_data = {
                'type': 'job_status_update',
                'job_id': job_id,
                'status': status,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            if tenant_id:
                message_data['tenant_id'] = tenant_id
            
            if additional_data:
                message_data.update(additional_data)
            
            # Convert to JSON string
            message_json = json.dumps(message_data)
            message_bytes = message_json.encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(self.topic_path, message_bytes)
            message_id = future.result()
            
            logger.info(f"Published job status notification: {job_id} -> {status}")
            return message_id
            
        except Exception as e:
            logger.error(f"Error publishing job status notification: {e}")
            return None
    
    def publish_error_notification(self, error_type: str, error_message: str, 
                                 context: Dict = None) -> Optional[str]:
        """Publish error notification.
        
        Args:
            error_type: Type of error
            error_message: Error message
            context: Optional context information
            
        Returns:
            Message ID if successful, None otherwise
        """
        if not self.publisher or not self.topic_path:
            logger.warning("Pub/Sub not configured, skipping notification")
            return None
        
        try:
            message_data = {
                'type': 'error_notification',
                'error_type': error_type,
                'error_message': error_message,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            
            if context:
                message_data['context'] = context
            
            # Convert to JSON string
            message_json = json.dumps(message_data)
            message_bytes = message_json.encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(self.topic_path, message_bytes)
            message_id = future.result()
            
            logger.info(f"Published error notification: {error_type}")
            return message_id
            
        except Exception as e:
            logger.error(f"Error publishing error notification: {e}")
            return None
    
    def publish_custom_message(self, message_type: str, data: Dict) -> Optional[str]:
        """Publish a custom message.
        
        Args:
            message_type: Type of the message
            data: Message data
            
        Returns:
            Message ID if successful, None otherwise
        """
        if not self.publisher or not self.topic_path:
            logger.warning("Pub/Sub not configured, skipping notification")
            return None
        
        try:
            message_data = {
                'type': message_type,
                'timestamp': datetime.utcnow().isoformat() + 'Z'
            }
            message_data.update(data)
            
            # Convert to JSON string
            message_json = json.dumps(message_data)
            message_bytes = message_json.encode('utf-8')
            
            # Publish message
            future = self.publisher.publish(self.topic_path, message_bytes)
            message_id = future.result()
            
            logger.info(f"Published custom message: {message_type}")
            return message_id
            
        except Exception as e:
            logger.error(f"Error publishing custom message: {e}")
            return None
    
    def test_connection(self) -> bool:
        """Test Pub/Sub connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        if not self.publisher or not self.topic_path:
            logger.warning("Pub/Sub not configured")
            return False
        
        try:
            # Try to get topic metadata
            topic = self.publisher.get_topic(request={"topic": self.topic_path})
            logger.info("Pub/Sub connection test successful")
            return True
            
        except NotFound:
            logger.error(f"Pub/Sub topic not found: {self.topic_path}")
            return False
        except Exception as e:
            logger.error(f"Pub/Sub connection test failed: {e}")
            return False
    
    def get_connection_info(self) -> Dict:
        """Get current connection configuration info.
        
        Returns:
            Dictionary with connection details
        """
        return {
            'project_id': self.config['project_id'],
            'pubsub_topic': self.config['pubsub_topic'],
            'topic_path': self.topic_path,
            'publisher_active': self.publisher is not None
        }