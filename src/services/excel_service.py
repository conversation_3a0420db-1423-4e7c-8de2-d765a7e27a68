"""Excel generation service for Marine HMM Reports API."""

import pandas as pd
import numpy as np
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils.dataframe import dataframe_to_rows
from datetime import datetime
import logging
from typing import Dict, List, Optional, Tuple
import tempfile
import os
from dal.database_connector import DatabaseConnector
from dal.bigquery_connector import BigQueryConnector

logger = logging.getLogger(__name__)


class ExcelService:
    """Service for generating Excel reports from marine risk data."""
    
    def __init__(self, db_connector: DatabaseConnector = None, bq_connector: BigQueryConnector = None):
        """Initialize Excel service with database connectors.
        
        Args:
            db_connector: PostgreSQL database connector
            bq_connector: BigQuery connector
        """
        self.db_connector = db_connector or DatabaseConnector()
        self.bq_connector = bq_connector or BigQueryConnector()
        
        # Style definitions
        self.header_style = {
            'font': Font(bold=True, color="FFFFFF", size=10),
            'fill': Pat<PERSON><PERSON>ill(start_color="1E3A5F", end_color="1E3A5F", fill_type="solid"),
            'alignment': Alignment(horizontal="center", vertical="center"),
            'border': Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        }
        
        self.data_style = {
            'font': Font(size=9),
            'alignment': Alignment(horizontal="left", vertical="center"),
            'border': Border(
                left=Side(style='thin', color="CCCCCC"),
                right=Side(style='thin', color="CCCCCC"),
                top=Side(style='thin', color="CCCCCC"),
                bottom=Side(style='thin', color="CCCCCC")
            )
        }
    
    def generate_excel_report(self, tenant_id: str) -> str:
        """Generate Excel report for a specific tenant.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            Path to the generated Excel file
        """
        logger.info(f"Starting Excel report generation for tenant: {tenant_id}")
        
        try:
            # Collect vessel data
            vessel_data = self._collect_vessel_data(tenant_id)
            
            # Aggregate account data
            account_data = self._aggregate_account_data(vessel_data)
            
            # Generate Excel file
            temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
            temp_path = temp_file.name
            temp_file.close()
            
            self._generate_combined_excel(account_data, vessel_data, temp_path)
            
            logger.info(f"Excel report generated successfully: {temp_path}")
            return temp_path
            
        except Exception as e:
            logger.error(f"Error generating Excel report: {e}")
            raise
    
    def _collect_vessel_data(self, tenant_id: str) -> pd.DataFrame:
        """Collect and merge all vessel-level data for a tenant.
        
        Args:
            tenant_id: Tenant identifier
            
        Returns:
            DataFrame with vessel data
        """
        logger.info(f"Collecting vessel data for tenant: {tenant_id}")
        
        # Get vessel policy information
        vessel_policy_query = """
        SELECT 
            imo,
            policy_id,
            policy_number,
            account_id,
            account_name
        FROM policy_detail.vessel_policy_info 
        WHERE tenant_id = :tenant_id
        """
        vessel_policy_df = self.db_connector.execute_query(
            vessel_policy_query, 
            {'tenant_id': tenant_id}
        )
        
        if vessel_policy_df.empty:
            logger.warning(f"No vessel policy data found for tenant: {tenant_id}")
            return pd.DataFrame()
        
        # Get risk scores
        risk_scores_query = """
        SELECT 
            imo,
            ROUND(score, 0) as vessel_risk_score,
            ROUND(detail_score, 0) as expected_loss,
            ROUND(freq_score, 0) as frequency,
            ROUND(sev_score, 0) as severity,
            ROUND(CASE 
                WHEN vessel_type_detail_score IS NULL OR vessel_type_detail_score < 0 
                THEN detail_score 
                ELSE vessel_type_detail_score 
            END, 0) as comparative_vessel_el
        FROM risk_scores.vessel_model_scores_10
        WHERE created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_model_scores_10)
        """
        risk_scores_df = self.db_connector.execute_query(risk_scores_query)
        
        # Get vessel risk drivers
        risk_drivers_query = """
        SELECT 
            vf.imo,
            vf.riskdriver AS "driverName",
            round(sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END)),4) AS "positiveInfluencePercentage",
            round(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)),4) AS "negativeInfluencePercentage",
            round(abs(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) - sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))),4) AS "netInfluencePercentage",
            CASE 
                WHEN sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) > sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))
                THEN 'POSITIVE'
                ELSE 'NEGATIVE'
            END AS "riskStatus"
        FROM risk_scores.vessel_feature_values_10 vf
        WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
        GROUP BY vf.imo, vf.riskdriver
        ORDER BY vf.imo, "netInfluencePercentage" DESC
        """
        risk_drivers_df = self.db_connector.execute_query(risk_drivers_query)
        
        # Get vessel features
        vessel_imos = vessel_policy_df['imo'].dropna().unique()
        imo_list = [int(imo) for imo in vessel_imos if pd.notna(imo)]
        
        if imo_list:
            imo_values = ','.join([str(imo) for imo in imo_list])
            features_query = f"""
            SELECT 
                ranked_vf.imo,
                ranked_vf.feature as "key",
                fd.field_name as "fieldName",
                fd.description as "description",
                fd.unit as "featureValueUnit",
                abs(round(ranked_vf.shap_impact_percentage,4))::real as "riskValue",
                ranked_vf.riskdriver as "riskDriver",
                abs(round(ranked_vf.shap_impact_percentage,4))::real as "featurePercentage",
                ranked_vf.shap_impact_sign as "riskStatus",
                abs(round(ranked_vf.feature_value,4))::real as "featureValue"
            FROM (
                SELECT vf.imo, vf.feature, vf.shap_impact_percentage, vf.riskdriver, vf.shap_impact_sign, vf.feature_value, vf.created_on,
                       ROW_NUMBER() OVER (PARTITION BY vf.imo ORDER BY abs(vf.shap_impact_percentage) DESC) as rn
                FROM risk_scores.vessel_feature_values_10 vf
                WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
                AND vf.imo IN ({imo_values})
            ) ranked_vf
            LEFT JOIN risk_scores.feature_dictionary_10 fd ON ranked_vf.feature = fd.name AND ranked_vf.created_on = fd.created_on
            WHERE ranked_vf.rn <= 15
            ORDER BY ranked_vf.imo, ranked_vf.shap_impact_percentage DESC
            """
            features_df = self.db_connector.execute_query(features_query)
        else:
            features_df = pd.DataFrame()
        
        # Get inspection data from BigQuery
        project_id = self.bq_connector.config['project_id']
        inspection_query = f"""
        SELECT 
            lrimoshipno as imo,
            SAFE_CAST(yearofbuild AS INT64) as yearofbuild,
            COUNT(*) as total_inspections,
            SUM(COALESCE(numberofdefects, 0)) as total_defects,
            SUM(CASE WHEN shipdetained = true THEN 1 ELSE 0 END) as total_detentions
        FROM `{project_id}.public.inspections`
        WHERE lrimoshipno IS NOT NULL
        GROUP BY lrimoshipno, yearofbuild
        """
        inspection_df = self.bq_connector.execute_query(inspection_query)
        
        # Get vessel names
        vessel_names_query = """
        SELECT 
            imo,
            vessel_name
        FROM vessel_detail.vessel_summary
        WHERE vessel_name IS NOT NULL
        """
        vessel_names_df = self.db_connector.execute_query(vessel_names_query)
        
        # Merge all data
        vessel_data = vessel_policy_df.merge(risk_scores_df, on='imo', how='left')
        vessel_data = vessel_data.merge(inspection_df, on='imo', how='left')
        vessel_data = vessel_data.merge(vessel_names_df, on='imo', how='left')
        
        # Fill NaN values and calculate derived metrics
        vessel_data = self._process_vessel_metrics(vessel_data)
        
        # Process risk drivers and features
        vessel_data = self._process_risk_drivers_and_features(vessel_data, risk_drivers_df, features_df)
        
        logger.info(f"Collected data for {len(vessel_data)} vessels")
        return vessel_data
    
    def _process_vessel_metrics(self, vessel_data: pd.DataFrame) -> pd.DataFrame:
        """Process and calculate vessel metrics.
        
        Args:
            vessel_data: Raw vessel data
            
        Returns:
            Processed vessel data with calculated metrics
        """
        # Fill NaN and <NA> values in numeric columns
        numeric_columns = ['vessel_risk_score', 'expected_loss', 'frequency', 'severity', 'comparative_vessel_el']
        for col in numeric_columns:
            if col in vessel_data.columns:
                vessel_data[col] = vessel_data[col].fillna(0)
        
        # Calculate derived metrics
        vessel_data['total_expected_loss'] = vessel_data['expected_loss'].fillna(0)
        
        # Handle inspection metrics
        if 'total_inspections' in vessel_data.columns and 'total_defects' in vessel_data.columns:
            vessel_data['total_inspections'] = vessel_data['total_inspections'].fillna(0)
            vessel_data['total_defects'] = vessel_data['total_defects'].fillna(0)
            
            vessel_data['avg_defects_per_inspection'] = (
                vessel_data['total_defects'] / vessel_data['total_inspections'].replace(0, np.nan)
            ).fillna(0).round(0)
        else:
            vessel_data['avg_defects_per_inspection'] = 0
        
        if 'total_inspections' in vessel_data.columns and 'total_detentions' in vessel_data.columns:
            vessel_data['total_detentions'] = vessel_data['total_detentions'].fillna(0)
            
            vessel_data['avg_detentions_per_inspection'] = (
                vessel_data['total_detentions'] / vessel_data['total_inspections'].replace(0, np.nan)
            ).fillna(0).round(0)
        else:
            vessel_data['avg_detentions_per_inspection'] = 0
        
        return vessel_data
    
    def _format_feature_value_with_unit(self, feature_value, feature_unit):
        """Format feature value with unit concatenation based on business rules.
        
        Rules:
        - Round values to 2 decimal places
        - Don't concatenate unit if it's: binary, char, number, index, value
        - For other units, concatenate as "value unit"
        """
        # Handle missing or invalid values
        if pd.isna(feature_value) or feature_value == '' or feature_value == '-':
            return '-'
        
        # Convert to float and round to 2 decimal places
        try:
            numeric_value = float(feature_value)
            rounded_value = round(numeric_value, 2)
        except (ValueError, TypeError):
            return str(feature_value)
        
        # Handle unit concatenation
        if pd.isna(feature_unit) or feature_unit == '':
            return str(rounded_value)
        
        # Units that should NOT be concatenated
        no_concat_units = {'binary', 'char', 'number', 'index', 'value'}
        unit_str = str(feature_unit).lower().strip()
        
        if unit_str in no_concat_units:
            return str(rounded_value)
        else:
            return f"{rounded_value} {feature_unit}"
    
    def _process_risk_drivers_and_features(self, vessel_data: pd.DataFrame, risk_drivers_df: pd.DataFrame, features_df: pd.DataFrame) -> pd.DataFrame:
        """Process risk drivers and features with unit concatenation."""
        try:
            # Process Risk Drivers
            if not risk_drivers_df.empty:
                vessel_risk_drivers = {}
                for imo in vessel_data['imo'].unique():
                    if pd.isna(imo):
                        continue
                    vessel_drivers = risk_drivers_df[risk_drivers_df['imo'] == imo].sort_values('netInfluencePercentage', ascending=False)
                    vessel_risk_drivers[imo] = vessel_drivers.head(10)
                
                max_risk_drivers = max([len(drivers) for drivers in vessel_risk_drivers.values()]) if vessel_risk_drivers else 0
                max_risk_drivers = min(max_risk_drivers, 10)
                
                # Initialize Risk Driver columns
                for i in range(1, max_risk_drivers + 1):
                    vessel_data[f'Risk driver {i}'] = '-'
                    vessel_data[f'Risk driver {i} (in %)'] = '-'
                    vessel_data[f'Risk driver sign {i}'] = '-'
                
                # Populate Risk Driver data
                for imo, drivers in vessel_risk_drivers.items():
                    mask = vessel_data['imo'] == imo
                    for i, (_, driver_row) in enumerate(drivers.iterrows(), 1):
                        if i <= max_risk_drivers:
                            driver_name = driver_row['driverName']
                            net_influence = round(driver_row['netInfluencePercentage'], 2)
                            risk_status = driver_row['riskStatus']
                            
                            if pd.isna(driver_name) or driver_name == '' or driver_name == '-':
                                vessel_data.loc[mask, f'Risk driver {i}'] = '-'
                                vessel_data.loc[mask, f'Risk driver {i} (in %)'] = '-'
                                vessel_data.loc[mask, f'Risk driver sign {i}'] = '-'
                            elif net_influence == 0:
                                vessel_data.loc[mask, f'Risk driver {i}'] = driver_name
                                vessel_data.loc[mask, f'Risk driver {i} (in %)'] = 0
                                vessel_data.loc[mask, f'Risk driver sign {i}'] = 'NEUTRAL'
                            else:
                                vessel_data.loc[mask, f'Risk driver {i}'] = driver_name
                                vessel_data.loc[mask, f'Risk driver {i} (in %)'] = net_influence
                                vessel_data.loc[mask, f'Risk driver sign {i}'] = risk_status
            
            # Process Features (Influential Factors)
            if not features_df.empty:
                vessel_features = {}
                for imo in vessel_data['imo'].unique():
                    if pd.isna(imo):
                        continue
                    vessel_feat = features_df[features_df['imo'] == imo].sort_values('featurePercentage', ascending=False)
                    vessel_features[imo] = vessel_feat.head(10)
                
                max_features = max([len(features) for features in vessel_features.values()]) if vessel_features else 0
                max_features = min(max_features, 10)
                
                # Initialize Influential Factor columns
                for i in range(1, max_features + 1):
                    vessel_data[f'Influential factor {i}'] = '-'
                    vessel_data[f'Influential factor {i} value'] = '-'
                    vessel_data[f'Influential factor {i} percentage'] = '-'
                    vessel_data[f'Influential factor {i} (Risk-driver)'] = '-'
                    vessel_data[f'Influential factor {i} sign'] = '-'
                
                # Populate Influential Factor data
                for imo, features in vessel_features.items():
                    mask = vessel_data['imo'] == imo
                    for i, (_, feature_row) in enumerate(features.iterrows(), 1):
                        if i <= max_features:
                            field_name = feature_row['fieldName'] if pd.notna(feature_row['fieldName']) else feature_row['key']
                            feature_value = feature_row['featureValue']
                            feature_unit = feature_row['featureValueUnit'] if pd.notna(feature_row['featureValueUnit']) else ''
                            feature_percentage = feature_row['featurePercentage'] if pd.notna(feature_row['featurePercentage']) else None
                            risk_driver = feature_row['riskDriver']
                            risk_status = feature_row['riskStatus']
                            
                            if pd.isna(field_name) or field_name == '' or field_name == '-':
                                vessel_data.loc[mask, f'Influential factor {i}'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} value'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} percentage'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} (Risk-driver)'] = '-'
                                vessel_data.loc[mask, f'Influential factor {i} sign'] = '-'
                            else:
                                # Format feature value with unit concatenation logic
                                formatted_value = self._format_feature_value_with_unit(feature_value, feature_unit)
                                formatted_percentage = round(float(feature_percentage), 2) if feature_percentage is not None else '-'
                                
                                vessel_data.loc[mask, f'Influential factor {i}'] = field_name
                                vessel_data.loc[mask, f'Influential factor {i} value'] = formatted_value
                                vessel_data.loc[mask, f'Influential factor {i} percentage'] = formatted_percentage
                                vessel_data.loc[mask, f'Influential factor {i} (Risk-driver)'] = risk_driver if pd.notna(risk_driver) else '-'
                                vessel_data.loc[mask, f'Influential factor {i} sign'] = risk_status if pd.notna(risk_status) else '-'
            
            logger.info("Risk drivers and features processing completed successfully")
            return vessel_data
            
        except Exception as e:
            logger.error(f"Error processing risk drivers and features: {e}")
            return vessel_data
    
    def _clean_na_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean NA values that cause Excel export issues."""
        df_clean = df.copy()
        for col in df_clean.columns:
            if df_clean[col].dtype in ['object', 'string']:
                # For string columns, replace NA with '-'
                df_clean[col] = df_clean[col].fillna('-')
            else:
                # For numeric columns, replace NA with 0
                df_clean[col] = df_clean[col].fillna(0)
        return df_clean
    
    def _aggregate_account_data(self, vessel_data: pd.DataFrame) -> pd.DataFrame:
        """Aggregate vessel data to account level.
        
        Args:
            vessel_data: Vessel-level data
            
        Returns:
            Account-level aggregated data
        """
        logger.info("Aggregating data to account level")
        
        if vessel_data.empty:
            return pd.DataFrame()
        
        try:
            account_groups = vessel_data.groupby(['policy_id', 'account_name'])
            
            # Get account-level risk drivers and features
            all_vessel_imos = vessel_data['imo'].dropna().unique()
            if len(all_vessel_imos) > 0:
                all_account_risk_drivers_data, all_account_features_data = self._fetch_account_risk_data(all_vessel_imos)
            else:
                all_account_risk_drivers_data = pd.DataFrame()
                all_account_features_data = pd.DataFrame()
            
            # Determine max columns needed
            all_account_risk_drivers = []
            all_account_features = []
            for (policy_id, account_name), group in account_groups:
                vessel_imos = group['imo'].dropna().unique()
                policy_risk_drivers = all_account_risk_drivers_data[all_account_risk_drivers_data['imo'].isin(vessel_imos)] if not all_account_risk_drivers_data.empty else pd.DataFrame()
                policy_features = all_account_features_data[all_account_features_data['imo'].isin(vessel_imos)] if not all_account_features_data.empty else pd.DataFrame()
                
                risk_driver_count = len(policy_risk_drivers.groupby('driverName').first()) if not policy_risk_drivers.empty else 0
                feature_count = len(policy_features.groupby(['key', 'riskDriver']).first()) if not policy_features.empty else 0
                
                all_account_risk_drivers.append(min(risk_driver_count, 10))
                all_account_features.append(min(feature_count, 10))
            
            max_risk_drivers = max(all_account_risk_drivers) if all_account_risk_drivers else 0
            max_features = max(all_account_features) if all_account_features else 0
            
            account_data = []
            
            for (policy_id, account_name), group in account_groups:
                # Get policy_number from the first row of the group
                policy_number = group['policy_number'].iloc[0] if 'policy_number' in group.columns and not group['policy_number'].empty else policy_id
                record = {
                    'Account Name': str(account_name) if account_name is not None else '-',
                    'Policy Number': str(policy_number) if policy_number is not None else '-',
                    'Account Risk Score': round(float(group['vessel_risk_score'].mean()), 0) if not group['vessel_risk_score'].isna().all() else 0,
                    'Frequency': round(float(group['frequency'].mean()), 0) if not group['frequency'].isna().all() else 0,
                    'Severity': round(float(group['severity'].mean()), 0) if not group['severity'].isna().all() else 0,
                    'Total expected loss (in USD)': round(float(group['total_expected_loss'].sum()), 0) if not group['total_expected_loss'].isna().all() else 0,
                    'Comparative Account EL (in USD)': round(float(group['comparative_vessel_el'].sum()), 0) if 'comparative_vessel_el' in group.columns and not group['comparative_vessel_el'].isna().all() else 0,
                    'No. of Vessels': int(len(group))
                }
                
                # Calculate average year of build
                if 'yearofbuild' in group.columns:
                    valid_years = group['yearofbuild'].dropna()
                    record['Avg. year of build'] = int(round(float(valid_years.mean()))) if len(valid_years) > 0 else '-'
                else:
                    record['Avg. year of build'] = '-'
                
                # Calculate inspection metrics
                total_defects = group['total_defects'].fillna(0).sum()
                total_inspections = group['total_inspections'].fillna(0).sum()
                record['Average no. of defects per inspection'] = round(float(total_defects / total_inspections), 0) if total_inspections > 0 else 0
                
                total_detentions = group['total_detentions'].fillna(0).sum()
                record['Average no. of detentions per inspection'] = round(float(total_detentions / total_inspections), 0) if total_inspections > 0 else 0
                
                # Process account-level risk drivers and features
                vessel_imos = group['imo'].dropna().unique()
                account_risk_drivers = self._get_account_risk_drivers(all_account_risk_drivers_data, vessel_imos)
                account_features = self._get_account_features(all_account_features_data, vessel_imos)
                
                # Add risk driver columns
                for i in range(1, max_risk_drivers + 1):
                    if i <= len(account_risk_drivers):
                        driver = account_risk_drivers[i-1]
                        record[f'Risk driver {i}'] = driver['driverName']
                        record[f'Risk driver {i} (in %)'] = round(driver['netInfluencePercentage'], 2)
                        record[f'Risk driver sign {i}'] = driver['riskStatus']
                    else:
                        record[f'Risk driver {i}'] = '-'
                        record[f'Risk driver {i} (in %)'] = '-'
                        record[f'Risk driver sign {i}'] = '-'
                
                # Initialize all feature columns with hyphens first
                for i in range(1, max_features + 1):
                    record[f'Influential factor {i}'] = '-'
                    record[f'Influential factor {i} value'] = '-'
                    record[f'Influential factor {i} percentage'] = '-'
                    record[f'Influential factor {i} (Risk-driver)'] = '-'
                    record[f'Influential factor {i} sign'] = '-'
                
                # Add feature columns
                for i in range(1, max_features + 1):
                    if i <= len(account_features):
                        feature = account_features[i-1]
                        field_name = feature['fieldName'] if pd.notna(feature['fieldName']) else feature['key']
                        feature_value = feature['featureValue']
                        feature_unit = feature['featureValueUnit']
                        feature_percentage = feature['featurePercentage']
                        risk_driver = feature['riskDriver']
                        risk_status = feature['riskStatus']
                        
                        if field_name == '-' or field_name == '' or pd.isna(field_name):
                            continue  # Keep hyphens
                        else:
                            formatted_value = self._format_feature_value_with_unit(feature_value, feature_unit)
                            formatted_percentage = round(float(feature_percentage), 2) if feature_percentage is not None and pd.notna(feature_percentage) else '-'
                            
                            record[f'Influential factor {i}'] = field_name
                            record[f'Influential factor {i} value'] = formatted_value
                            record[f'Influential factor {i} percentage'] = formatted_percentage
                            record[f'Influential factor {i} (Risk-driver)'] = risk_driver if pd.notna(risk_driver) else '-'
                            record[f'Influential factor {i} sign'] = risk_status if pd.notna(risk_status) else '-'
                
                account_data.append(record)
            
            return pd.DataFrame(account_data)
            
        except Exception as e:
            logger.error(f"Error aggregating account data: {e}")
            raise
    
    def _generate_combined_excel(self, account_data: pd.DataFrame, vessel_data: pd.DataFrame, output_path: str):
        """Generate combined Excel file with account and vessel sheets.
        
        Args:
            account_data: Account-level data
            vessel_data: Vessel-level data
            output_path: Output file path
        """
        logger.info("Generating combined Excel file")
        
        try:
            workbook = Workbook()
            
            # Remove default sheet
            workbook.remove(workbook.active)
            
            # Create Account sheet
            if not account_data.empty:
                self._create_account_sheet(workbook, account_data)
            
            # Create Vessel sheet
            if not vessel_data.empty:
                self._create_vessel_sheet(workbook, vessel_data)
            
            # Save workbook
            workbook.save(output_path)
            logger.info(f"Excel file saved to: {output_path}")
            
        except Exception as e:
            logger.error(f"Error generating Excel file: {e}")
            raise
    
    def _create_account_sheet(self, workbook: Workbook, account_data: pd.DataFrame):
        """Create account-level sheet in the workbook.
        
        Args:
            workbook: Excel workbook
            account_data: Account-level data
        """
        sheet = workbook.create_sheet("Account Level Report")
        
        # Clean data and add to sheet
        account_data_clean = self._clean_na_values(account_data)
        
        # Add data to sheet
        for r in dataframe_to_rows(account_data_clean, index=False, header=True):
            sheet.append(r)
        
        # Apply styling
        self._apply_sheet_styling(sheet, len(account_data.columns))
    
    def _create_vessel_sheet(self, workbook: Workbook, vessel_data: pd.DataFrame):
        """Create vessel-level sheet in the workbook.
        
        Args:
            workbook: Excel workbook
            vessel_data: Vessel-level data
        """
        # Define vessel sheet columns with display names (excluding account_id and policy_id)
        vessel_columns_mapping = {
            'imo': 'IMO',
            'vessel_name': 'Vessel Name',
            'policy_number': 'Policy Number',
            'account_name': 'Account Name',
            'vessel_risk_score': 'Vessel Risk Score',
            'expected_loss': 'Expected Loss',
            'frequency': 'Frequency',
            'severity': 'Severity',
            'comparative_vessel_el': 'Comparative Vessel EL',
            'yearofbuild': 'Year Of Build',
            'total_inspections': 'Total Inspections',
            'total_defects': 'Total Defects',
            'total_detentions': 'Total Detentions',
            'avg_defects_per_inspection': 'Avg Defects Per Inspection',
            'avg_detentions_per_inspection': 'Avg Detentions Per Inspection'
        }

        # Filter and rename columns for vessel sheet
        vessel_sheet_data = self._filter_and_rename_vessel_columns(vessel_data, vessel_columns_mapping)

        sheet = workbook.create_sheet("Vessel Level Report")
        
        # Clean data and add to sheet
        vessel_sheet_data_clean = self._clean_na_values(vessel_sheet_data)
        
        # Add data to sheet
        for r in dataframe_to_rows(vessel_sheet_data_clean, index=False, header=True):
            sheet.append(r)
        
        # Apply styling
        self._apply_sheet_styling(sheet, len(vessel_sheet_data.columns))

    def _filter_and_rename_vessel_columns(self, vessel_data: pd.DataFrame, columns_mapping: dict) -> pd.DataFrame:
        """Filter and rename vessel columns for Excel output."""
        # Get available columns that exist in both the data and our mapping
        available_columns = []
        for col in columns_mapping.keys():
            if col in vessel_data.columns:
                available_columns.append(col)

        # Add risk driver columns (they follow a pattern)
        risk_driver_columns = []
        for col in vessel_data.columns:
            if col.startswith('Risk driver '):
                risk_driver_columns.append(col)
                available_columns.append(col)

        # Add feature columns (they follow a pattern)
        feature_columns = []
        for col in vessel_data.columns:
            if col.startswith('Feature ') or col.startswith('Influential factor '):
                feature_columns.append(col)
                available_columns.append(col)

        # Filter the dataframe to only include available columns
        filtered_data = vessel_data[available_columns].copy()

        # Rename columns using the mapping (only for base columns, not risk drivers/features)
        rename_dict = {}
        for col in filtered_data.columns:
            if col in columns_mapping:
                rename_dict[col] = columns_mapping[col]
            # Risk driver and feature columns keep their original names

        if rename_dict:
            filtered_data = filtered_data.rename(columns=rename_dict)

        return filtered_data

    def _apply_sheet_styling(self, sheet, num_columns: int):
        """Apply styling to a worksheet.
        
        Args:
            sheet: Worksheet to style
            num_columns: Number of columns in the sheet
        """
        # Style header row
        for col in range(1, num_columns + 1):
            cell = sheet.cell(row=1, column=col)
            cell.font = self.header_style['font']
            cell.fill = self.header_style['fill']
            cell.alignment = self.header_style['alignment']
            cell.border = self.header_style['border']
        
        # Style data rows
        for row in range(2, sheet.max_row + 1):
            for col in range(1, num_columns + 1):
                cell = sheet.cell(row=row, column=col)
                cell.font = self.data_style['font']
                cell.alignment = self.data_style['alignment']
                cell.border = self.data_style['border']
        
        # Auto-adjust column widths
        for column in sheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            sheet.column_dimensions[column_letter].width = adjusted_width
    
    def _fetch_account_risk_data(self, all_vessel_imos) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """Fetch account-level risk drivers and features data."""
        try:
            imo_list = [int(imo) for imo in all_vessel_imos if pd.notna(imo)]
            if not imo_list:
                return pd.DataFrame(), pd.DataFrame()
            
            imo_values = ','.join([str(imo) for imo in imo_list])
            
            # Get risk drivers data
            risk_drivers_query = f"""
            SELECT 
                vf.imo,
                vf.riskdriver AS "driverName",
                sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END)) AS "positiveInfluencePercentage",
                sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) AS "negativeInfluencePercentage",
                abs(sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) - sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))) AS "netInfluencePercentage",
                CASE 
                    WHEN sum((case when vf.shap_impact_sign = 'POSITIVE' then vf.shap_impact_percentage else 0 END)) > sum((case when vf.shap_impact_sign = 'NEGATIVE' then vf.shap_impact_percentage else 0 END))
                    THEN 'POSITIVE'
                    ELSE 'NEGATIVE'
                END AS "riskStatus"
            FROM risk_scores.vessel_feature_values_10 vf
            WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
            AND vf.imo IN ({imo_values})
            GROUP BY vf.imo, vf.riskdriver
            """
            
            all_risk_drivers_df = self.db_connector.execute_query(risk_drivers_query)
            
            # Get features data
            features_query = f"""
            SELECT 
                ranked_vf.imo,
                ranked_vf.feature as "key",
                fd.field_name as "fieldName",
                fd.description as "description",
                fd.unit as "featureValueUnit",
                abs(round(ranked_vf.shap_impact_percentage,4)) as "riskValue",
                ranked_vf.riskdriver as "riskDriver",
                abs(round(ranked_vf.shap_impact_percentage,4)) as "featurePercentage",
                ranked_vf.shap_impact_sign as "riskStatus",
                abs(round(ranked_vf.feature_value,4)) as "featureValue"
            FROM (
                SELECT vf.imo, vf.feature, vf.shap_impact_percentage, vf.riskdriver, vf.shap_impact_sign, vf.feature_value, vf.created_on,
                       ROW_NUMBER() OVER (PARTITION BY vf.imo ORDER BY abs(vf.shap_impact_percentage) DESC) as rn
                FROM risk_scores.vessel_feature_values_10 vf
                WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
                AND vf.imo IN ({imo_values})
            ) ranked_vf
            LEFT JOIN risk_scores.feature_dictionary_10 fd ON ranked_vf.feature = fd.name AND ranked_vf.created_on = fd.created_on
            WHERE ranked_vf.rn <= 15
            """
            
            all_features_df = self.db_connector.execute_query(features_query)
            
            return all_risk_drivers_df, all_features_df
            
        except Exception as e:
            logger.error(f"Error fetching account risk data: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def _get_account_risk_drivers(self, all_risk_drivers_df: pd.DataFrame, vessel_imos) -> List[Dict]:
        """Get aggregated risk drivers for account."""
        if all_risk_drivers_df.empty:
            return []
        
        policy_risk_drivers = all_risk_drivers_df[all_risk_drivers_df['imo'].isin(vessel_imos)]
        
        if policy_risk_drivers.empty:
            return []
        
        # Aggregate by risk driver
        driver_agg = policy_risk_drivers.groupby('driverName').agg({
            'positiveInfluencePercentage': 'mean',
            'negativeInfluencePercentage': 'mean',
            'netInfluencePercentage': 'mean'
        }).reset_index()
        
        # Determine overall risk status
        driver_agg['riskStatus'] = driver_agg.apply(
            lambda row: 'POSITIVE' if row['negativeInfluencePercentage'] > row['positiveInfluencePercentage'] else 'NEGATIVE',
            axis=1
        )
        
        # Sort by net influence and return top 10
        driver_agg = driver_agg.sort_values('netInfluencePercentage', ascending=False).head(10)
        
        return driver_agg.to_dict('records')
    
    def _get_account_features(self, all_features_df: pd.DataFrame, vessel_imos) -> List[Dict]:
        """Get aggregated features for account."""
        if all_features_df.empty:
            return []
        
        policy_features = all_features_df[all_features_df['imo'].isin(vessel_imos)]
        
        if policy_features.empty:
            return []
        
        # Aggregate by feature and risk driver
        feature_agg = policy_features.groupby(['key', 'fieldName', 'description', 'featureValueUnit', 'riskDriver']).agg({
            'riskValue': 'mean',
            'featurePercentage': 'mean',
            'featureValue': 'mean'
        }).reset_index()
        
        # Determine overall risk status
        feature_agg['riskStatus'] = 'POSITIVE'  # Simplified for account level
        
        # Sort by feature percentage and return top 10
        feature_agg = feature_agg.sort_values('featurePercentage', ascending=False).head(10)
        
        return feature_agg.to_dict('records')