#!/usr/bin/env python3
"""Debug script to check influential factors in vessel data."""

import sys
import os
import logging
import pandas as pd

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from dal.database_connector import DatabaseConnector
from dal.bigquery_connector import BigQueryConnector
from services.streaming_excel_service import StreamingExcelService

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_influential_factors(tenant_id: str = "test_tenant"):
    """Debug influential factors for a specific tenant."""
    try:
        logger.info(f"Starting debug for tenant: {tenant_id}")
        
        # Initialize connectors
        db_connector = DatabaseConnector()
        bq_connector = BigQueryConnector()
        
        # Test database connection
        logger.info("Testing database connection...")
        test_query = "SELECT 1 as test"
        test_result = db_connector.execute_query(test_query)
        logger.info(f"Database connection test: {len(test_result)} rows")
        
        # Test BigQuery connection
        logger.info("Testing BigQuery connection...")
        bq_test = bq_connector.test_connection()
        logger.info(f"BigQuery connection test: {bq_test}")
        
        # Get vessel policy data
        logger.info("Getting vessel policy data...")
        vessel_policy_query = """
        SELECT imo, policy_id, policy_number, account_id, account_name
        FROM policy_detail.vessel_policy_info
        WHERE tenant_id = :tenant_id
        LIMIT 5
        """
        vessel_policy_df = db_connector.execute_query(vessel_policy_query, {'tenant_id': tenant_id})
        logger.info(f"Found {len(vessel_policy_df)} vessels for tenant {tenant_id}")
        
        if vessel_policy_df.empty:
            logger.warning("No vessels found for this tenant")
            return
        
        # Get IMO list
        vessel_imos = vessel_policy_df['imo'].dropna().unique()
        imo_list = [int(imo) for imo in vessel_imos if pd.notna(imo)]
        logger.info(f"IMO list: {imo_list}")
        
        if not imo_list:
            logger.warning("No valid IMOs found")
            return
        
        # Test features query
        logger.info("Testing features query...")
        imo_values = ','.join([str(imo) for imo in imo_list])
        features_query = f"""
        SELECT 
            ranked_vf.imo,
            ranked_vf.feature as "key",
            fd.field_name as "fieldName",
            fd.description as "description",
            fd.unit as "featureValueUnit",
            abs(round(ranked_vf.shap_impact_percentage,4))::real as "riskValue",
            ranked_vf.riskdriver as "riskDriver",
            abs(round(ranked_vf.shap_impact_percentage,4))::real as "featurePercentage",
            ranked_vf.shap_impact_sign as "riskStatus",
            abs(round(ranked_vf.feature_value,4))::real as "featureValue"
        FROM (
            SELECT vf.imo, vf.feature, vf.shap_impact_percentage, vf.riskdriver, vf.shap_impact_sign, vf.feature_value, vf.created_on,
                   ROW_NUMBER() OVER (PARTITION BY vf.imo ORDER BY abs(vf.shap_impact_percentage) DESC) as rn
            FROM risk_scores.vessel_feature_values_10 vf
            WHERE vf.created_on = (SELECT MAX(created_on) FROM risk_scores.vessel_feature_values_10)
            AND vf.imo IN ({imo_values})
        ) ranked_vf
        LEFT JOIN risk_scores.feature_dictionary_10 fd ON ranked_vf.feature = fd.name AND ranked_vf.created_on = fd.created_on
        WHERE ranked_vf.rn <= 10
        ORDER BY ranked_vf.imo, ranked_vf.shap_impact_percentage DESC
        """
        
        try:
            features_df = db_connector.execute_query(features_query)
            logger.info(f"Features query returned {len(features_df)} records")
            if not features_df.empty:
                logger.info(f"Sample features: {features_df.head()}")
                logger.info(f"Features columns: {list(features_df.columns)}")
            else:
                logger.warning("Features query returned no data")
        except Exception as e:
            logger.error(f"Features query failed: {e}")
        
        # Test country data query
        logger.info("Testing country data query...")
        service = StreamingExcelService(db_connector, bq_connector)
        try:
            country_data = service._get_country_data(imo_list)
            logger.info(f"Country data query returned {len(country_data)} records")
            if not country_data.empty:
                logger.info(f"Sample country data: {country_data.head()}")
                logger.info(f"Country data columns: {list(country_data.columns)}")
            else:
                logger.warning("Country data query returned no data")
        except Exception as e:
            logger.error(f"Country data query failed: {e}")
        
        # Test vessel data chunk processing
        logger.info("Testing vessel data chunk processing...")
        try:
            chunk_data = service._get_vessel_chunk_data(vessel_policy_df, imo_list)
            logger.info(f"Chunk processing returned {len(chunk_data)} rows with {len(chunk_data.columns)} columns")
            
            # Check for influential factor columns
            influential_columns = [col for col in chunk_data.columns if col.startswith('Influential factor')]
            logger.info(f"Found {len(influential_columns)} influential factor columns")
            if influential_columns:
                logger.info(f"Influential factor columns: {influential_columns[:5]}...")  # Show first 5
            else:
                logger.warning("No influential factor columns found!")
                
        except Exception as e:
            logger.error(f"Chunk processing failed: {e}")
        
        logger.info("Debug completed")
        
    except Exception as e:
        logger.error(f"Debug failed: {e}")
        raise

if __name__ == "__main__":
    tenant_id = sys.argv[1] if len(sys.argv) > 1 else "test_tenant"
    debug_influential_factors(tenant_id)
